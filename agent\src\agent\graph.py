import os
import time
from datetime import datetime
from typing import Any

from langchain.chat_models import init_chat_model
from langgraph.prebuilt import create_react_agent
from langchain_mcp_adapters.client import MultiServerMCPClient
from langchain_core.callbacks import Callbacks
from langchain.schema.runnable import RunnableConfig

from contextlib import asynccontextmanager

from galileo import galileo_context
from galileo.handlers.langchain import GalileoAsyncCallback

from agent.util.prompt import PROMPT
from agent.util.tools import TOOLS
from agent.util.states import State

#Initialize the Galileo callback handler
external_id = f"sales-agent-{int(time.time())}"
current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
session_name = f"Sales Agent test - {current_time}"
galileo_context.start_session(name=session_name, external_id=external_id)
galileo_callback = GalileoAsyncCallback()

@asynccontextmanager
async def make_graph():

    mcp_client = MultiServerMCPClient(
        {
            "pricing": {
                "url": "http://mcp-pricing:8000/mcp/",
                "transport": "streamable_http",
            },
            "search": {
                "url": "http://mcp-search:8000/mcp/",
                "transport": "streamable_http",
            }
        }
    )

    try:
        # Gather all tools for the agent
        tools = []
        tools.extend(TOOLS)

        pricing_tools = await mcp_client.get_tools(server_name="pricing")
        tools.extend(pricing_tools)

        search_tools = await mcp_client.get_tools(server_name="search")
        tools.extend(search_tools)

        #model_name = os.environ.get("CHAT_MODEL")
        chat_model = init_chat_model("azure_openai:gpt-4.1")

        config: dict[str, Any] = {"configurable": {"thread_id": external_id}}
        callbacks: Callbacks = [galileo_callback]
        runnable_config = RunnableConfig(callbacks=callbacks, **config)

        # Create the ReAct agent with proper configuration
        # The key is to ensure that tool execution is properly handled
        agent = create_react_agent(
            model=chat_model,
            tools=tools,
            prompt=PROMPT,
            state_schema=State,
            # Explicitly set checkpointer to None to avoid state issues
            checkpointer=None,
        )

        # Configure the agent with the default config
        agent = agent.with_config(runnable_config)

        yield agent
    finally:
        # Cleanup MCP client connections
        await mcp_client.close()